# Clean Matches Script - Enhanced Batch Processing

## Overview
The `clean_matches.py` script has been enhanced to process the `matches.json` file using the Gemini API with robust batch processing capabilities, while preserving the original `generate()` function completely unchanged.

## Features

### 1. **Dual Mode Operation**
- **Manual Mode**: Run `python clean_matches.py` to use the original interactive mode
- **Batch Mode**: Run `python clean_matches.py batch` to process the entire matches.json file

### 2. **API Key Rotation**
- Automatically rotates between 5 API keys every 1000 objects
- Prevents rate limiting and ensures continuous processing
- Keys are loaded from the `.env` file

### 3. **Checkpoint System**
- Saves progress to `last_matches.json` after every 50 objects
- Automatically resumes from the last processed object if interrupted
- Prevents data loss during long processing sessions

### 4. **Error Handling**
- Robust retry logic with exponential backoff (up to 3 attempts per object)
- Invalid responses and errors are saved to `error.json` with detailed information
- Processing continues even if individual objects fail

### 5. **Progress Tracking**
- Real-time progress reporting every 10 objects
- Shows processing rate (objects per minute)
- Displays estimated time of completion
- Tracks total processed objects and error count

### 6. **Response Validation**
- Validates that API responses are properly formatted JSON arrays
- Extracts JSON from markdown-formatted responses
- Ensures data integrity before saving

## Setup

### 1. Install Dependencies
```bash
pip install google-genai python-dotenv
```

### 2. Environment Configuration
The `.env` file is already configured with 5 API keys:
```
GEMINI_API_KEY=AIzaSyDG264QNADYMgTIzJS8Pa3LAJdgx85Fbxo
GEMINI_API_KEY2=AIzaSyAoF2MK2tdA3CwDGnfoz4oVr7KRxbJ-jZ0
GEMINI_API_KEY3=AIzaSyAbRO3dlyIhTtzTO-7gC3B_5jUp6RnlMaY
GEMINI_API_KEY4=AIzaSyD2GnWC0000au7PRjvvYVNsQ3OrpUOksDQ
GEMINI_API_KEY5=AIzaSyDnbVGh0LBzwriJazsQoeM3ywf717Zfhfs
```

## Usage

### Batch Processing (Recommended)
```bash
cd interface
python clean_matches.py batch
```

### Manual Mode (Original Functionality)
```bash
cd interface
python clean_matches.py
```

## Output Files

### 1. `last_matches.json`
- Contains all successfully processed and cleaned match groups
- Updated incrementally every 50 objects
- Used for checkpoint/resume functionality

### 2. `error.json`
- Contains objects that failed processing after 3 retry attempts
- Includes error details, timestamps, and original object data
- Useful for debugging and manual review

## Processing Statistics

The script provides detailed statistics including:
- Total objects processed
- Processing rate (objects per minute)
- Error count and percentage
- Estimated time of completion
- Total processing time

## Resume Capability

If the script is interrupted:
1. Simply run `python clean_matches.py batch` again
2. The script will automatically detect the last processed object
3. Processing will resume from where it left off
4. No data will be lost or duplicated

## Error Recovery

- Failed objects are saved to `error.json` with full context
- Processing continues with the next object
- You can manually review and reprocess failed objects if needed
- The script implements exponential backoff to handle temporary API issues

## Performance Optimization

- API key rotation prevents rate limiting
- Small delays (0.1s) between requests to avoid overwhelming the API
- Incremental saving prevents memory issues with large datasets
- Efficient JSON parsing and validation

## Data Integrity

- All responses are validated before saving
- Checkpoint system ensures no data loss
- Error tracking maintains audit trail
- Original `generate()` function logic preserved exactly

## Monitoring

The script provides real-time monitoring:
```
Progress: 1250/224272 (0.6%)
Processed: 1250, Errors: 3
Rate: 45.2 objects/min, ETA: 82.1 minutes
Checkpoint saved at object 1251
```

This enhanced version maintains full compatibility with the original script while adding enterprise-grade batch processing capabilities.
